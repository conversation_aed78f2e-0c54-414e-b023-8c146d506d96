import cv2
import numpy as np
import matplotlib.pyplot as plt

image = cv2.imread('./cow.jpg')
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

plt.imshow(image_rgb)

channels = cv2.split(image_rgb)
colors = ['r', 'g', 'b']

for channel, color in zip(channels, colors):
    hist = cv2.calcHist([channel], [0], None, [256], [0, 256])
    plt.plot(hist, color=color)
    plt.xlim([0, 256])

plt.title('Histogram for Color Scale Picture')
plt.xlabel('Pixel Value')
plt.ylabel('Frequency')
plt.show()


image_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

h, s, v = cv2.split(image_hsv)

h_hist = cv2.calcHist([h], [0], None, [180], [0, 180])
s_hist = cv2.calcHist([s], [0], None, [256], [0, 256])
v_hist = cv2.calcHist([v], [0], None, [256], [0, 256])

fig, axes = plt.subplots(1, 3, figsize=(15, 5))

axes[0].plot(h_hist, color='Orange')
axes[0].title("Hue Histogram")
axes[0].set_xlabel("Bins")
axes[0].ylabel("Frequency")

axes[1].plot(s_hist, color='Orange')
axes[1].title("Saturation Histogram")
axes[1].set_xlabel("Bins")
axes[1].ylabel("Frequency")

axes[2].plot(v_hist, color='Orange')
axes[2].title("Value Histogram")
axes[2].set_xlabel("Bins")
axes[2].ylabel("Frequency")


