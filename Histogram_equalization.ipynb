import cv2
import matplotlib.pyplot as plt
import numpy as np



image = cv2.imread('./cow.jpg')
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

plt.imshow(image_rgb)

image_greyscale = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)

hist, bins = np.histogram(image_greyscale.flatten(), 256, [0,256])

# Perform histogram equalization

equalised_image = cv2.equalizeHist(image_greyscale)

equalised_hist, bins = np.histogram(equalised_image.flatten(), 256, [0,256])

# plot the results
plt.figure(figsize=(12,6))

plt.subplot(2,2,1)
plt.imshow(image_greyscale, cmap='grey')
plt.title('Original image')

plt.subplot(2,2,2)
plt.plot(hist, color='blue')
plt.title('Original image histogram')

plt.subplot(2,2,3)
plt.imshow(equalised_image, cmap='grey')
plt.title('Original image')

plt.subplot(2,2,4)
plt.plot(equalised_hist, color='green')
plt.title('Equalised image histogram')

plt.tight_layout()
plt.show()


image_yuv = cv2.cvtColor(image_rgb, cv2.COLOR_BGR2YUV)

image_yuv[:,:,0] = cv2.equalizeHist(image_yuv[:,:,0])

equalised_image = cv2.cvtColor(image_yuv, cv2.COLOR_YUV2BGR)

equalised_image_rgb = cv2.cvtColor(equalised_image, cv2.COLOR_BGR2RGB)

# plot the result
plt.figure(figsize=(12,6))

plt.subplot(1,2,1)
plt.imshow(image_rgb)
plt.title('Original image')

plt.subplot(1,2,2)
plt.imshow(equalised_image_rgb)
plt.title('Equalised image')

plt.tight_layout()
plt.show()

