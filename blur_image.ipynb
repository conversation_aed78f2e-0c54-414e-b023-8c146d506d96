import cv2
import numpy as np
import matplotlib.pyplot as plt



image = cv2.imread("./animal.jpg")

# convert to RBG and GRAYSCALE
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
image_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

kernel_s = 5
average_blur = cv2.blur(image_gray, (kernel_s, kernel_s))
plt.imshow(average_blur, cmap="gray")


# gaussian blur
kernel_s = 11
gaussian_blur = cv2.GaussianBlur(image_gray, (kernel_s, kernel_s), 0)
plt.imshow(gaussian_blur, cmap="gray")

# Median blur
kernel_s = 17
median_blur = cv2.medianBlur(image_gray, kernel_s)
plt.imshow(median_blur, cmap="gray")

# to compare all the image in one frame
plt.subplot(2, 2, 1)
plt.imshow(image_gray, cmap="gray")
plt.title("Original Image")

plt.subplot(2, 2, 2)
plt.imshow(average_blur, cmap="gray")
plt.title("Average Blur")

plt.subplot(2, 2, 3)
plt.imshow(gaussian_blur, cmap="gray")
plt.title("Gaussian Blur")

plt.subplot(2, 2, 4)
plt.imshow(median_blur, cmap="gray")
plt.title("Median Blur")



