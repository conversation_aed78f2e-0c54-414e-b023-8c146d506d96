import cv2
import matplotlib.pyplot as plt
import numpy as np


image = cv2.imread('./cow.jpg')
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

plt.imshow(image_rgb)

image_greyscale = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)

equalised_image = cv2.equalizeHist(image_greyscale)

# Create CLAHE object
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
clahe_image = clahe.apply(image_greyscale)

# Plot the results
plt.figure(figsize=(12,6))

# Original grayscale image
plt.subplot(1, 3, 1)
plt.imshow(image_greyscale, cmap='gray')
plt.title('Original image')

# Histogram Equalized image
plt.subplot(1, 3, 2)
plt.imshow(equalised_image, cmap='gray')
plt.title('Equalised image')

# CLAHE image
plt.subplot(1, 3, 3)
plt.imshow(clahe_image, cmap='gray')
plt.title('CLAHE image')

plt.tight_layout()
plt.show()


# Convert BGR image to YUV
yuv_image = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)


yuv_image[:,:,0] = clahe.apply(yuv_image[:,:,0])

yuv_image_bgr = cv2.cvtColor(yuv_image, cv2.COLOR_YUV2BGR)


yuv_image_rgb = cv2.cvtColor(yuv_image_bgr, cv2.COLOR_BGR2RGB)
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)


plt.figure(figsize=(12,6))

plt.subplot(1, 2, 1)
plt.imshow(image_rgb)
plt.title('Original RGB Image')

plt.subplot(1, 2, 2)
plt.imshow(yuv_image_rgb)
plt.title('CLAHE on Luminance Channel')

plt.tight_layout()
plt.show()


