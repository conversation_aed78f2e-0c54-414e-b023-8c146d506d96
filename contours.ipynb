import cv2
import matplotlib.pyplot as plt
import numpy as np

image = cv2.imread('./image_shape.jpg')
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
image_gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)

plt.imshow(image_rgb)

image_gray = cv2.Canny(image_gray, 100, 200)
plt.imshow(image_gray, cmap='gray')



# Find contours
contours, hierarchy = cv2.findContours(image_gray, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

# Copy original image to draw contours
contour_image = image.copy()
cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 2)

# Plot results
plt.figure(figsize=(12,6))

plt.subplot(1, 2, 1)
plt.imshow(cv2.cvtColor(contour_image, cv2.COLOR_BGR2RGB))
plt.title('Contours on Original Image')

plt.subplot(1, 2, 2)
plt.imshow(image_gray, cmap='gray')
plt.title('Grayscale Image')

plt.tight_layout()
plt.show()


