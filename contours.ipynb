import cv2
import matplotlib.pyplot as plt
import numpy as np

image = cv2.imread('./image_shape.jpg')
image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
image_gray = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2GRAY)

plt.imshow(image_rgb)

gray = cv2.Canny(image_gray, 100, 200)
plt.imshow(gray, cmap='gray')




image = cv2.imread('image_shape.jpg')  # Replace with your image path
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# 2. Apply thresholding (required for findContours)
_, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

# 3. Find contours (use binary image, not grayscale)
contours, hierarchy = cv2.findContours(binary, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)

# 4. Draw contours
contour_image = image.copy()
cv2.drawContours(contour_image, contours, -1, (255, 255, 0), 10)

# 5. Display results
plt.figure(figsize=(12, 6))
plt.subplot(1, 3, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.title("Original Image")

plt.subplot(1, 3, 2)
plt.imshow(binary, cmap='gray')
plt.title("Binary Mask")

plt.subplot(1, 3, 3)
plt.imshow(cv2.cvtColor(contour_image, cv2.COLOR_BGR2RGB))
plt.title("Contours")

plt.show()

import cv2
import matplotlib.pyplot as plt

# 1. Read and preprocess
image = cv2.imread('image_shape.jpg')  # Replace with your image path
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
_, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

# 2. Different contour retrieval methods
methods = [
    ("RETR_EXTERNAL", cv2.RETR_EXTERNAL),  # Only outer contours
    ("RETR_LIST", cv2.RETR_LIST),          # All contours, no hierarchy
    ("RETR_TREE", cv2.RETR_TREE),          # All contours with hierarchy
    ("RETR_CCOMP", cv2.RETR_CCOMP)         # Two-level hierarchy
]

# 3. Plot results for each method
plt.figure(figsize=(14, 8))

for i, (name, mode) in enumerate(methods, start=1):
    contours, hierarchy = cv2.findContours(binary, mode, cv2.CHAIN_APPROX_SIMPLE)
    contour_image = image.copy()
    cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 8)
    
    plt.subplot(2, 2, i)
    plt.imshow(cv2.cvtColor(contour_image, cv2.COLOR_BGR2RGB))
    plt.title(f"{name} ({len(contours)} contours)")

plt.tight_layout()
plt.show()




