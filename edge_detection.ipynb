import cv2
import numpy as np
import matplotlib.pyplot as plt

image = cv2.imread("./animal.jpg")

# sobel edge detection
sobel_x = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=5)
sobel_y = cv2.<PERSON>bel(image, cv2.CV_64F, 0, 1, ksize=5)

sobel_combined = cv2.addWeighted(sobel_x, 0.5, sobel_y, 0.5, 0)

# laplacian edge detection
laplacian = cv2.Laplacian(image, cv2.CV_64F, ksize=3)
laplacian_abs = cv2.convertScaleAbs(laplacian)

# canny
canny_egde = cv2.Canny(image, 180, 200)



laplacian_abs

title = ["Original Image", "Sobel X", "Sobel Y", "Sobel Combined", "Laplacian", "Canny"]
images = [image, sobel_x, sobel_y, sobel_combined, laplacian_abs, canny_egde]

plt.figure(figsize=(18, 13))
for i in range(len(images)):
    plt.subplot(2, 3, i+1)
    plt.imshow(images[i], cmap="gray")
    plt.title(title[i])
    plt.axis("off")
    plt.show()

